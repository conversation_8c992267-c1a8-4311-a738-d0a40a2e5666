import numpy as np
import random
import heapq

nums=list(range(9))
random.shuffle(nums)
grid=np.array(nums).reshape(3,3)
print(grid)  

true=1
true_grid=np.zeros((3,3),dtype=int)

for i in range(3):
    for j in range(3):
        true_grid[i][j]=true
        true+=1
        if(true==9):
            true=0

print(true_grid)     
        

def manhattan_distance(point1, point2):
    return abs(point1[0] - point2[0]) + abs(point1[1] - point2[1])

def calculate_heuristics(grid, true_grid):
    h_cost = 0
    for i in range(3):
        for j in range(3):
            if grid[i][j] != 0:
                point1 = (i, j)
                point2 = np.where(true_grid == grid[i][j])
                goal_pos = (point2[0][0], point2[1][0])
                h_cost += manhattan_distance(point1, goal_pos)
    return h_cost


def astar(grid,true_grid):
    # Implements the A* algorithm to find the shortest path in a grid.
    open_set = []  
    heapq.heappush(open_set,(0,grid)) #f_cost, grid
    closed_set=set()
    start_state=tuple(grid.flatten())
    goal_state=tuple(true_grid.flatten())
    g_cost=0
    h_cost
    
    
    
    
    
    
    
    
    
    